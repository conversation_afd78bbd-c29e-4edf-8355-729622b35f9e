# Database
DATABASE_URL="postgresql://username:password@localhost:5432/task_saas_db?schema=public"

# NextAuth.js (for future authentication setup)
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Optional: Redis for queue system (Phase 2+)
# REDIS_URL="redis://localhost:6379"

# Optional: Email service (Phase 2+)
# EMAIL_SERVER_HOST="smtp.example.com"
# EMAIL_SERVER_PORT=587
# EMAIL_SERVER_USER="<EMAIL>"
# EMAIL_SERVER_PASSWORD="your-password"
# EMAIL_FROM="<EMAIL>"
