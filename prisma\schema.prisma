// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// PHASE 1: Core MVP Models
// ============================================================================

model Organization {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users User[]
  tasks Task[]

  @@map("organizations")
}

model User {
  id             String   @id @default(cuid())
  name           String
  email          String   @unique
  password       String?  // For credential-based auth
  role           UserRole @default(AGENT)
  organizationId String

  // Basic agent properties for Phase 1
  maxConcurrentTasks Int @default(5)
  currentTaskCount   Int @default(0)
  status            UserStatus @default(AVAILABLE)

  // NextAuth fields
  emailVerified DateTime?
  image         String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  assignedTasks Task[]
  taskEvents    TaskEvent[]
  accounts      Account[]
  sessions      Session[]

  @@index([organizationId])
  @@index([organizationId, status])
  @@index([email])
  @@map("users")
}

model Task {
  id             String   @id @default(cuid())
  organizationId String
  title          String
  description    String?
  priority       TaskPriority @default(MEDIUM)
  type           String   @default("general")
  estimatedDuration Int?  // minutes
  source         String   @default("manual")

  // Assignment tracking
  assignedTo     String?
  assignedAt     DateTime?
  status         TaskStatus @default(PENDING)

  // SLA tracking (basic for Phase 1)
  slaDeadline      DateTime?
  responseDeadline DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  assignedUser User?        @relation(fields: [assignedTo], references: [id], onDelete: SetNull)
  events       TaskEvent[]

  @@index([organizationId])
  @@index([organizationId, status])
  @@index([assignedTo])
  @@index([status])
  @@map("tasks")
}

model TaskEvent {
  id      String    @id @default(cuid())
  taskId  String
  userId  String?
  type    TaskEventType
  data    Json?
  createdAt DateTime @default(now())

  // Relations
  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([taskId])
  @@index([createdAt])
  @@map("task_events")
}

// ============================================================================
// ENUMS
// ============================================================================

enum UserStatus {
  AVAILABLE
  BUSY
  AWAY
  OFFLINE
}

enum UserRole {
  ADMIN
  AGENT
}

enum TaskStatus {
  PENDING
  ASSIGNED
  IN_PROGRESS
  COMPLETED
  ESCALATED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TaskEventType {
  CREATED
  ASSIGNED
  STATUS_CHANGED
  UPDATED
  COMPLETED
  ESCALATED
  COMMENT_ADDED
}

// ============================================================================
// NEXTAUTH MODELS
// ============================================================================

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}
