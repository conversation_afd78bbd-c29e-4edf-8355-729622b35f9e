import {
  Organization,
  User,
  Task,
  TaskEvent,
  UserStatus,
  UserRole,
  TaskStatus,
  TaskPriority,
  TaskEventType,
} from "@prisma/client";

// ============================================================================
// EXTENDED TYPES WITH RELATIONS
// ============================================================================

export type OrganizationWithRelations = Organization & {
  users?: User[];
  tasks?: Task[];
  _count?: {
    users: number;
    tasks: number;
  };
};

export type UserWithRelations = User & {
  organization?: Organization;
  assignedTasks?: Task[];
  taskEvents?: TaskEvent[];
  _count?: {
    assignedTasks: number;
    taskEvents: number;
  };
};

export type TaskWithRelations = Task & {
  organization?: Organization;
  assignedUser?: User | null;
  events?: TaskEvent[];
  _count?: {
    events: number;
  };
};

export type TaskEventWithRelations = TaskEvent & {
  task?: Task;
  user?: User | null;
};

// ============================================================================
// API TYPES
// ============================================================================

export interface CreateTaskRequest {
  title: string;
  description?: string;
  priority?: TaskPriority;
  type?: string;
  estimatedDuration?: number;
  source?: string;
  responseDeadline?: Date;
  slaDeadline?: Date;
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  priority?: TaskPriority;
  type?: string;
  estimatedDuration?: number;
  status?: TaskStatus;
  assignedTo?: string | null;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  password?: string;
  role?: UserRole;
  maxConcurrentTasks?: number;
  status?: UserStatus;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  role?: UserRole;
  maxConcurrentTasks?: number;
  status?: UserStatus;
}

export interface CreateOrganizationRequest {
  name: string;
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  organizationId?: string;
  role?: UserRole;
}

export interface AuthUser {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  organizationId: string;
  image?: string;
}

// ============================================================================
// DASHBOARD TYPES
// ============================================================================

export interface DashboardStats {
  totalTasks: number;
  pendingTasks: number;
  assignedTasks: number;
  completedTasks: number;
  totalUsers: number;
  availableUsers: number;
  avgResponseTime?: number;
  avgCompletionTime?: number;
}

export interface UserWorkload {
  userId: string;
  userName: string;
  currentTasks: number;
  maxTasks: number;
  utilizationRate: number;
  status: UserStatus;
}

export interface TaskDistribution {
  priority: TaskPriority;
  count: number;
  percentage: number;
}

// ============================================================================
// ROUTING TYPES (for future phases)
// ============================================================================

export interface Assignment {
  taskId: string;
  agentId: string;
  assignedAt: Date;
  confidence: number;
}

export type RoutingStrategy =
  | "round_robin"
  | "weighted_round_robin"
  | "least_loaded"
  | "best_match";

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface TaskFilters {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  type?: string[];
  assignedTo?: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export interface UserFilters {
  status?: UserStatus[];
  organizationId?: string;
}

// ============================================================================
// PAGINATION TYPES
// ============================================================================

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// RE-EXPORT PRISMA ENUMS
// ============================================================================

export {
  UserStatus,
  UserRole,
  TaskStatus,
  TaskPriority,
  TaskEventType,
  type Organization,
  type User,
  type Task,
  type TaskEvent,
};
