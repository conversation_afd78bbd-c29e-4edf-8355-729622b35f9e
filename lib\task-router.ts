import { prisma } from './prisma'
import { UserStatus, TaskStatus, TaskEventType } from '@prisma/client'
import type { Task, User } from '@prisma/client'

// ============================================================================
// TYPES
// ============================================================================

export interface RoutingResult {
  success: boolean
  assignedAgent?: User
  reason?: string
  error?: string
}

export interface EligibleAgent extends User {
  utilizationRate: number
  lastAssignedAt?: Date
}

export type RoutingStrategy = 'round_robin' | 'least_loaded' | 'weighted_round_robin'

// ============================================================================
// TASK ROUTER CLASS
// ============================================================================

export class TaskRouter {
  private organizationId: string

  constructor(organizationId: string) {
    this.organizationId = organizationId
  }

  /**
   * Main routing function - assigns a task to the best available agent
   */
  async routeTask(taskId: string, strategy: RoutingStrategy = 'round_robin'): Promise<RoutingResult> {
    try {
      // Get the task
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        include: { organization: true }
      })

      if (!task) {
        return { success: false, error: 'Task not found' }
      }

      if (task.assignedTo) {
        return { success: false, reason: 'Task already assigned' }
      }

      // Get eligible agents
      const eligibleAgents = await this.getEligibleAgents()

      if (eligibleAgents.length === 0) {
        return { success: false, reason: 'No eligible agents available' }
      }

      // Select agent based on strategy
      const selectedAgent = await this.selectAgent(eligibleAgents, strategy)

      if (!selectedAgent) {
        return { success: false, reason: 'No suitable agent found' }
      }

      // Assign the task
      const assignmentResult = await this.assignTaskToAgent(taskId, selectedAgent.id)

      if (assignmentResult.success) {
        return {
          success: true,
          assignedAgent: selectedAgent,
          reason: `Assigned using ${strategy} strategy`
        }
      } else {
        return assignmentResult
      }

    } catch (error) {
      console.error('Error in task routing:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown routing error' 
      }
    }
  }

  /**
   * Get all eligible agents for task assignment
   */
  private async getEligibleAgents(): Promise<EligibleAgent[]> {
    const agents = await prisma.user.findMany({
      where: {
        organizationId: this.organizationId,
        status: UserStatus.AVAILABLE,
      },
    })

    // Filter agents with available capacity and calculate metrics
    const eligibleAgents: EligibleAgent[] = []

    for (const agent of agents) {
      // Check if agent has capacity
      if (agent.currentTaskCount >= agent.maxConcurrentTasks) {
        continue
      }

      // Calculate utilization rate
      const utilizationRate = agent.maxConcurrentTasks > 0 
        ? (agent.currentTaskCount / agent.maxConcurrentTasks) * 100 
        : 0

      // Get last assignment time for round-robin
      const lastAssignment = await prisma.task.findFirst({
        where: {
          assignedTo: agent.id,
          assignedAt: { not: null }
        },
        orderBy: { assignedAt: 'desc' },
        select: { assignedAt: true }
      })

      eligibleAgents.push({
        ...agent,
        utilizationRate,
        lastAssignedAt: lastAssignment?.assignedAt || undefined
      })
    }

    return eligibleAgents
  }

  /**
   * Select the best agent based on routing strategy
   */
  private async selectAgent(
    agents: EligibleAgent[], 
    strategy: RoutingStrategy
  ): Promise<EligibleAgent | null> {
    if (agents.length === 0) return null

    switch (strategy) {
      case 'round_robin':
        return this.roundRobinSelection(agents)
      
      case 'least_loaded':
        return this.leastLoadedSelection(agents)
      
      case 'weighted_round_robin':
        return this.weightedRoundRobinSelection(agents)
      
      default:
        return this.roundRobinSelection(agents)
    }
  }

  /**
   * Round-robin selection: Choose agent who was assigned least recently
   */
  private roundRobinSelection(agents: EligibleAgent[]): EligibleAgent {
    // Sort by last assigned time (null/undefined first, then oldest first)
    const sortedAgents = agents.sort((a, b) => {
      if (!a.lastAssignedAt && !b.lastAssignedAt) return 0
      if (!a.lastAssignedAt) return -1
      if (!b.lastAssignedAt) return 1
      return a.lastAssignedAt.getTime() - b.lastAssignedAt.getTime()
    })

    return sortedAgents[0]
  }

  /**
   * Least loaded selection: Choose agent with lowest utilization
   */
  private leastLoadedSelection(agents: EligibleAgent[]): EligibleAgent {
    return agents.reduce((least, current) => 
      current.utilizationRate < least.utilizationRate ? current : least
    )
  }

  /**
   * Weighted round-robin: Consider both load and recency
   */
  private weightedRoundRobinSelection(agents: EligibleAgent[]): EligibleAgent {
    const now = new Date().getTime()
    
    const scoredAgents = agents.map(agent => {
      // Load factor (lower is better)
      const loadScore = 100 - agent.utilizationRate
      
      // Recency factor (longer since last assignment is better)
      const timeSinceAssignment = agent.lastAssignedAt 
        ? now - agent.lastAssignedAt.getTime()
        : now // If never assigned, give max score
      
      const recencyScore = Math.min(timeSinceAssignment / (1000 * 60 * 60), 100) // Max 100 points for 1+ hours
      
      // Combined score (equal weight to load and recency)
      const totalScore = (loadScore * 0.6) + (recencyScore * 0.4)
      
      return { agent, score: totalScore }
    })

    // Sort by score (highest first) and return the best agent
    scoredAgents.sort((a, b) => b.score - a.score)
    return scoredAgents[0].agent
  }

  /**
   * Assign task to specific agent with database transaction
   */
  private async assignTaskToAgent(taskId: string, agentId: string): Promise<RoutingResult> {
    try {
      // Use transaction to ensure consistency
      const result = await prisma.$transaction(async (tx) => {
        // Double-check agent availability
        const agent = await tx.user.findUnique({
          where: { id: agentId }
        })

        if (!agent) {
          throw new Error('Agent not found')
        }

        if (agent.currentTaskCount >= agent.maxConcurrentTasks) {
          throw new Error('Agent capacity exceeded')
        }

        if (agent.status !== UserStatus.AVAILABLE) {
          throw new Error('Agent not available')
        }

        // Update task
        const updatedTask = await tx.task.update({
          where: { id: taskId },
          data: {
            assignedTo: agentId,
            assignedAt: new Date(),
            status: TaskStatus.ASSIGNED,
          },
          include: {
            assignedUser: true,
            organization: true,
          },
        })

        // Update agent task count
        await tx.user.update({
          where: { id: agentId },
          data: {
            currentTaskCount: { increment: 1 },
          },
        })

        // Create task event
        await tx.taskEvent.create({
          data: {
            taskId,
            userId: agentId,
            type: TaskEventType.ASSIGNED,
            data: {
              assignedTo: agentId,
              assignedAt: new Date(),
              routingStrategy: 'round_robin',
            },
          },
        })

        return { task: updatedTask, agent }
      })

      return {
        success: true,
        assignedAgent: result.agent,
        reason: 'Task assigned successfully'
      }

    } catch (error) {
      console.error('Error assigning task to agent:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Assignment failed'
      }
    }
  }

  /**
   * Get routing statistics for monitoring
   */
  async getRoutingStats() {
    const [totalAgents, availableAgents, totalTasks, assignedTasks] = await Promise.all([
      prisma.user.count({ where: { organizationId: this.organizationId } }),
      prisma.user.count({ 
        where: { 
          organizationId: this.organizationId,
          status: UserStatus.AVAILABLE 
        } 
      }),
      prisma.task.count({ where: { organizationId: this.organizationId } }),
      prisma.task.count({ 
        where: { 
          organizationId: this.organizationId,
          status: TaskStatus.ASSIGNED 
        } 
      }),
    ])

    return {
      totalAgents,
      availableAgents,
      totalTasks,
      assignedTasks,
      routingCapacity: availableAgents > 0 ? 'available' : 'limited'
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a router instance for an organization
 */
export function createTaskRouter(organizationId: string): TaskRouter {
  return new TaskRouter(organizationId)
}

/**
 * Auto-assign a task using the default routing strategy
 */
export async function autoAssignTask(
  taskId: string, 
  organizationId: string,
  strategy: RoutingStrategy = 'round_robin'
): Promise<RoutingResult> {
  const router = createTaskRouter(organizationId)
  return router.routeTask(taskId, strategy)
}
