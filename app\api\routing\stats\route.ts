import { NextRequest, NextResponse } from 'next/server'
import { createTaskRouter } from '@/lib/task-router'
import { getDemoOrgId } from '@/lib/db-utils'
import { prisma } from '@/lib/prisma'
import { UserStatus, TaskStatus } from '@prisma/client'

// GET /api/routing/stats - Get routing and assignment statistics
export async function GET(request: NextRequest) {
  try {
    const organizationId = await getDemoOrgId()
    const router = createTaskRouter(organizationId)

    // Get basic routing stats
    const routingStats = await router.getRoutingStats()

    // Get detailed agent statistics
    const agents = await prisma.user.findMany({
      where: { organizationId },
      include: {
        _count: {
          select: {
            assignedTasks: true,
          },
        },
      },
    })

    const agentStats = agents.map(agent => ({
      id: agent.id,
      name: agent.name,
      email: agent.email,
      status: agent.status,
      currentTasks: agent.currentTaskCount,
      maxTasks: agent.maxConcurrentTasks,
      totalAssignedTasks: agent._count.assignedTasks,
      utilizationRate: agent.maxConcurrentTasks > 0 
        ? (agent.currentTaskCount / agent.maxConcurrentTasks) * 100 
        : 0,
      isAvailable: agent.status === UserStatus.AVAILABLE && 
                   agent.currentTaskCount < agent.maxConcurrentTasks,
    }))

    // Get task distribution by status
    const taskDistribution = await prisma.task.groupBy({
      by: ['status'],
      where: { organizationId },
      _count: {
        status: true,
      },
    })

    const taskStats = {
      pending: 0,
      assigned: 0,
      in_progress: 0,
      completed: 0,
      escalated: 0,
    }

    taskDistribution.forEach(stat => {
      taskStats[stat.status.toLowerCase() as keyof typeof taskStats] = stat._count.status
    })

    // Get recent assignment activity
    const recentAssignments = await prisma.taskEvent.findMany({
      where: {
        type: 'ASSIGNED',
        task: {
          organizationId,
        },
      },
      include: {
        task: {
          select: {
            id: true,
            title: true,
            priority: true,
            type: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 10,
    })

    // Calculate assignment metrics
    const totalCapacity = agents.reduce((sum, agent) => sum + agent.maxConcurrentTasks, 0)
    const usedCapacity = agents.reduce((sum, agent) => sum + agent.currentTaskCount, 0)
    const availableCapacity = totalCapacity - usedCapacity

    const systemUtilization = totalCapacity > 0 ? (usedCapacity / totalCapacity) * 100 : 0

    // Get assignment success rate (tasks assigned vs pending)
    const totalTasks = taskStats.pending + taskStats.assigned + taskStats.in_progress + taskStats.completed + taskStats.escalated
    const assignedTasksCount = taskStats.assigned + taskStats.in_progress + taskStats.completed
    const assignmentSuccessRate = totalTasks > 0 ? (assignedTasksCount / totalTasks) * 100 : 0

    return NextResponse.json({
      success: true,
      data: {
        routing: routingStats,
        agents: {
          total: agentStats.length,
          available: agentStats.filter(a => a.isAvailable).length,
          busy: agentStats.filter(a => a.status === UserStatus.BUSY).length,
          away: agentStats.filter(a => a.status === UserStatus.AWAY).length,
          offline: agentStats.filter(a => a.status === UserStatus.OFFLINE).length,
          details: agentStats,
        },
        tasks: {
          distribution: taskStats,
          total: totalTasks,
        },
        capacity: {
          total: totalCapacity,
          used: usedCapacity,
          available: availableCapacity,
          utilizationRate: systemUtilization,
        },
        performance: {
          assignmentSuccessRate,
          averageUtilization: agentStats.length > 0 
            ? agentStats.reduce((sum, a) => sum + a.utilizationRate, 0) / agentStats.length 
            : 0,
        },
        recentActivity: recentAssignments.map(event => ({
          id: event.id,
          taskId: event.task.id,
          taskTitle: event.task.title,
          taskPriority: event.task.priority,
          taskType: event.task.type,
          agentId: event.user?.id,
          agentName: event.user?.name,
          assignedAt: event.createdAt,
          routingData: event.data,
        })),
      },
      timestamp: new Date(),
    })

  } catch (error) {
    console.error('Error fetching routing stats:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'STATS_FETCH_FAILED',
          message: 'Failed to fetch routing statistics',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}
