import { prisma } from "./prisma";
import {
  TaskStatus,
  TaskPriority,
  UserStatus,
  TaskEventType,
  type TaskFilters,
  type UserFilters,
  type PaginationParams,
  type PaginatedResponse,
  type TaskWithRelations,
  type UserWithRelations,
  type DashboardStats,
  type UserWorkload,
} from "./types";

// ============================================================================
// TASK OPERATIONS
// ============================================================================

export async function getTasks(
  organizationId: string,
  filters?: TaskFilters,
  pagination?: PaginationParams
): Promise<PaginatedResponse<TaskWithRelations>> {
  const page = pagination?.page || 1;
  const limit = pagination?.limit || 10;
  const skip = (page - 1) * limit;

  const where = {
    organizationId,
    ...(filters?.status && { status: { in: filters.status } }),
    ...(filters?.priority && { priority: { in: filters.priority } }),
    ...(filters?.type && { type: { in: filters.type } }),
    ...(filters?.assignedTo && { assignedTo: { in: filters.assignedTo } }),
    ...(filters?.dateRange && {
      createdAt: {
        gte: filters.dateRange.from,
        lte: filters.dateRange.to,
      },
    }),
  };

  const [tasks, total] = await Promise.all([
    prisma.task.findMany({
      where,
      include: {
        assignedUser: true,
        organization: true,
        _count: {
          select: { events: true },
        },
      },
      skip,
      take: limit,
      orderBy: {
        [pagination?.sortBy || "createdAt"]: pagination?.sortOrder || "desc",
      },
    }),
    prisma.task.count({ where }),
  ]);

  return {
    data: tasks,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1,
    },
  };
}

export async function createTask(
  organizationId: string,
  data: {
    title: string;
    description?: string;
    priority?: TaskPriority;
    type?: string;
    estimatedDuration?: number;
    source?: string;
    responseDeadline?: Date;
    slaDeadline?: Date;
  }
) {
  const task = await prisma.task.create({
    data: {
      ...data,
      organizationId,
    },
    include: {
      assignedUser: true,
      organization: true,
    },
  });

  // Create task event
  await prisma.taskEvent.create({
    data: {
      taskId: task.id,
      type: TaskEventType.CREATED,
      data: {
        title: task.title,
        priority: task.priority,
        type: task.type,
      },
    },
  });

  return task;
}

export async function assignTask(taskId: string, userId: string) {
  const [task, user] = await Promise.all([
    prisma.task.findUnique({ where: { id: taskId } }),
    prisma.user.findUnique({ where: { id: userId } }),
  ]);

  if (!task || !user) {
    throw new Error("Task or user not found");
  }

  if (user.currentTaskCount >= user.maxConcurrentTasks) {
    throw new Error("User has reached maximum concurrent tasks");
  }

  const updatedTask = await prisma.task.update({
    where: { id: taskId },
    data: {
      assignedTo: userId,
      assignedAt: new Date(),
      status: TaskStatus.ASSIGNED,
    },
    include: {
      assignedUser: true,
      organization: true,
    },
  });

  // Update user task count
  await prisma.user.update({
    where: { id: userId },
    data: {
      currentTaskCount: {
        increment: 1,
      },
    },
  });

  // Create task event
  await prisma.taskEvent.create({
    data: {
      taskId,
      userId,
      type: TaskEventType.ASSIGNED,
      data: {
        assignedTo: userId,
        assignedAt: new Date(),
      },
    },
  });

  return updatedTask;
}

export async function updateTaskStatus(
  taskId: string,
  status: TaskStatus,
  userId?: string
) {
  const task = await prisma.task.findUnique({
    where: { id: taskId },
    include: { assignedUser: true },
  });

  if (!task) {
    throw new Error("Task not found");
  }

  const updatedTask = await prisma.task.update({
    where: { id: taskId },
    data: { status },
    include: {
      assignedUser: true,
      organization: true,
    },
  });

  // If task is completed, decrement user task count
  if (status === TaskStatus.COMPLETED && task.assignedTo) {
    await prisma.user.update({
      where: { id: task.assignedTo },
      data: {
        currentTaskCount: {
          decrement: 1,
        },
      },
    });
  }

  // Create task event
  await prisma.taskEvent.create({
    data: {
      taskId,
      userId,
      type: TaskEventType.STATUS_CHANGED,
      data: {
        oldStatus: task.status,
        newStatus: status,
      },
    },
  });

  return updatedTask;
}

// ============================================================================
// USER OPERATIONS
// ============================================================================

export async function getUsers(
  organizationId: string,
  filters?: UserFilters,
  pagination?: PaginationParams
): Promise<PaginatedResponse<UserWithRelations>> {
  const page = pagination?.page || 1;
  const limit = pagination?.limit || 10;
  const skip = (page - 1) * limit;

  const where = {
    organizationId,
    ...(filters?.status && { status: { in: filters.status } }),
  };

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      include: {
        organization: true,
        _count: {
          select: { assignedTasks: true },
        },
      },
      skip,
      take: limit,
      orderBy: {
        [pagination?.sortBy || "name"]: pagination?.sortOrder || "asc",
      },
    }),
    prisma.user.count({ where }),
  ]);

  return {
    data: users,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1,
    },
  };
}

export async function getAvailableUsers(organizationId: string) {
  return prisma.user.findMany({
    where: {
      organizationId,
      status: UserStatus.AVAILABLE,
      currentTaskCount: {
        lt: prisma.user.fields.maxConcurrentTasks,
      },
    },
    include: {
      organization: true,
      _count: {
        select: { assignedTasks: true },
      },
    },
  });
}

// ============================================================================
// UTILITY OPERATIONS
// ============================================================================

export async function getDemoOrgId(): Promise<string> {
  const org = await prisma.organization.findFirst({
    where: { name: "Demo Organization" },
  });

  if (!org) {
    throw new Error("Demo organization not found. Please run seed script.");
  }

  return org.id;
}

// ============================================================================
// DASHBOARD OPERATIONS
// ============================================================================

export async function getDashboardStats(
  organizationId: string
): Promise<DashboardStats> {
  const [
    totalTasks,
    pendingTasks,
    assignedTasks,
    completedTasks,
    totalUsers,
    availableUsers,
  ] = await Promise.all([
    prisma.task.count({ where: { organizationId } }),
    prisma.task.count({
      where: { organizationId, status: TaskStatus.PENDING },
    }),
    prisma.task.count({
      where: { organizationId, status: TaskStatus.ASSIGNED },
    }),
    prisma.task.count({
      where: { organizationId, status: TaskStatus.COMPLETED },
    }),
    prisma.user.count({ where: { organizationId } }),
    prisma.user.count({
      where: { organizationId, status: UserStatus.AVAILABLE },
    }),
  ]);

  return {
    totalTasks,
    pendingTasks,
    assignedTasks,
    completedTasks,
    totalUsers,
    availableUsers,
  };
}

export async function getUserWorkloads(
  organizationId: string
): Promise<UserWorkload[]> {
  const users = await prisma.user.findMany({
    where: { organizationId },
    select: {
      id: true,
      name: true,
      currentTaskCount: true,
      maxConcurrentTasks: true,
      status: true,
    },
  });

  return users.map((user) => ({
    userId: user.id,
    userName: user.name,
    currentTasks: user.currentTaskCount,
    maxTasks: user.maxConcurrentTasks,
    utilizationRate:
      user.maxConcurrentTasks > 0
        ? (user.currentTaskCount / user.maxConcurrentTasks) * 100
        : 0,
    status: user.status,
  }));
}
