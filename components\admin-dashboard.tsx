"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Users, 
  Activity, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  BarChart3,
  UserPlus
} from "lucide-react"
import { toast } from "sonner"
import { TaskCreationForm } from "./task-creation-form"

interface RoutingStats {
  routing: {
    totalAgents: number
    availableAgents: number
    totalTasks: number
    assignedTasks: number
  }
  agents: {
    total: number
    available: number
    busy: number
    away: number
    offline: number
    details: Array<{
      id: string
      name: string
      email: string
      status: string
      currentTasks: number
      maxTasks: number
      utilizationRate: number
      isAvailable: boolean
    }>
  }
  tasks: {
    distribution: {
      pending: number
      assigned: number
      in_progress: number
      completed: number
      escalated: number
    }
    total: number
  }
  capacity: {
    total: number
    used: number
    available: number
    utilizationRate: number
  }
  performance: {
    assignmentSuccessRate: number
    averageUtilization: number
  }
  recentActivity: Array<{
    id: string
    taskId: string
    taskTitle: string
    taskPriority: string
    agentName: string
    assignedAt: string
  }>
}

export function AdminDashboard() {
  const [stats, setStats] = useState<RoutingStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchStats = async () => {
    try {
      setRefreshing(true)
      const response = await fetch("/api/routing/stats")
      const result = await response.json()
      
      if (result.success) {
        setStats(result.data)
      } else {
        throw new Error(result.error?.message || "Failed to fetch stats")
      }
    } catch (error) {
      console.error("Error fetching stats:", error)
      toast.error("Failed to load dashboard data")
    } finally {
      setRefreshing(false)
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchStats, 30000)
    return () => clearInterval(interval)
  }, [])

  const handleTaskCreated = () => {
    // Refresh stats when a new task is created
    fetchStats()
  }

  if (loading) {
    return <div className="flex items-center justify-center p-8">Loading dashboard...</div>
  }

  if (!stats) {
    return <div className="flex items-center justify-center p-8">Failed to load dashboard data</div>
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "AVAILABLE": return "bg-green-500"
      case "BUSY": return "bg-red-500"
      case "AWAY": return "bg-yellow-500"
      case "OFFLINE": return "bg-gray-500"
      default: return "bg-gray-500"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "URGENT": return "bg-red-500 text-white"
      case "HIGH": return "bg-orange-500 text-white"
      case "MEDIUM": return "bg-yellow-500 text-white"
      case "LOW": return "bg-green-500 text-white"
      default: return "bg-gray-500 text-white"
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor team performance and task routing
          </p>
        </div>
        <div className="flex items-center gap-2">
          <TaskCreationForm onTaskCreated={handleTaskCreated} />
          <Button
            variant="outline"
            onClick={fetchStats}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <Users className="h-4 w-4" />
              Total Agents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.agents.total}</div>
            <div className="text-sm text-muted-foreground">
              {stats.agents.available} available
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Active Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.tasks.total}</div>
            <div className="text-sm text-muted-foreground">
              {stats.tasks.distribution.pending} pending
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              System Utilization
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.capacity.utilizationRate.toFixed(1)}%
            </div>
            <Progress value={stats.capacity.utilizationRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Assignment Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.performance.assignmentSuccessRate.toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">
              Success rate
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Agent Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Agent Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500" />
                      <span>Available</span>
                    </div>
                    <span className="font-medium">{stats.agents.available}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500" />
                      <span>Busy</span>
                    </div>
                    <span className="font-medium">{stats.agents.busy}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-yellow-500" />
                      <span>Away</span>
                    </div>
                    <span className="font-medium">{stats.agents.away}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-gray-500" />
                      <span>Offline</span>
                    </div>
                    <span className="font-medium">{stats.agents.offline}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Task Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Task Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>Pending</span>
                    <span className="font-medium">{stats.tasks.distribution.pending}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Assigned</span>
                    <span className="font-medium">{stats.tasks.distribution.assigned}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>In Progress</span>
                    <span className="font-medium">{stats.tasks.distribution.in_progress}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Completed</span>
                    <span className="font-medium">{stats.tasks.distribution.completed}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Escalated</span>
                    <span className="font-medium">{stats.tasks.distribution.escalated}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Capacity Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Capacity Overview</CardTitle>
              <CardDescription>
                System-wide task capacity and utilization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.capacity.total}</div>
                  <div className="text-sm text-muted-foreground">Total Capacity</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.capacity.used}</div>
                  <div className="text-sm text-muted-foreground">Used</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{stats.capacity.available}</div>
                  <div className="text-sm text-muted-foreground">Available</div>
                </div>
              </div>
              <Progress value={stats.capacity.utilizationRate} className="h-3" />
              <div className="text-center text-sm text-muted-foreground mt-2">
                {stats.capacity.utilizationRate.toFixed(1)}% utilized
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Agent Performance</CardTitle>
              <CardDescription>
                Individual agent status and workload
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Agent</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Current Tasks</TableHead>
                    <TableHead>Capacity</TableHead>
                    <TableHead>Utilization</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {stats.agents.details.map((agent) => (
                    <TableRow key={agent.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{agent.name}</div>
                          <div className="text-sm text-muted-foreground">{agent.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getStatusColor(agent.status)} text-white`}>
                          {agent.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{agent.currentTasks}</TableCell>
                      <TableCell>{agent.maxTasks}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={agent.utilizationRate} className="w-16 h-2" />
                          <span className="text-sm">{agent.utilizationRate.toFixed(1)}%</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Task Distribution</CardTitle>
              <CardDescription>
                Current task status breakdown
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
                {Object.entries(stats.tasks.distribution).map(([status, count]) => (
                  <div key={status} className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold">{count}</div>
                    <div className="text-sm text-muted-foreground capitalize">
                      {status.replace('_', ' ')}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest task assignments and updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recentActivity.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No recent activity
                  </div>
                ) : (
                  stats.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{activity.taskTitle}</div>
                        <div className="text-sm text-muted-foreground">
                          Assigned to {activity.agentName}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityColor(activity.taskPriority)}>
                          {activity.taskPriority}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(activity.assignedAt).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
