import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getDemoOrgId } from '@/lib/db-utils'
import { CreateUserRequest } from '@/lib/types'
import { UserStatus } from '@prisma/client'

// GET /api/agents - List agents
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Get query parameters
    const organizationId = searchParams.get('organizationId') || await getDemoOrgId()
    const status = searchParams.get('status') as UserStatus | null
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      organizationId,
    }

    if (status) {
      where.status = status
    }

    // Get agents with pagination
    const [agents, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              assignedTasks: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
        skip: offset,
        take: limit,
      }),
      prisma.user.count({ where }),
    ])

    // Calculate additional metrics for each agent
    const agentsWithMetrics = await Promise.all(
      agents.map(async (agent) => {
        const taskStats = await prisma.task.groupBy({
          by: ['status'],
          where: {
            assignedTo: agent.id,
          },
          _count: {
            status: true,
          },
        })

        const stats = {
          pending: 0,
          assigned: 0,
          in_progress: 0,
          completed: 0,
          escalated: 0,
        }

        taskStats.forEach((stat) => {
          stats[stat.status.toLowerCase() as keyof typeof stats] = stat._count.status
        })

        return {
          ...agent,
          taskStats: stats,
          utilizationRate: agent.maxConcurrentTasks > 0 
            ? (agent.currentTaskCount / agent.maxConcurrentTasks) * 100 
            : 0,
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: {
        agents: agentsWithMetrics,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit),
        },
      },
    })
  } catch (error) {
    console.error('Error fetching agents:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'FETCH_AGENTS_FAILED',
          message: 'Failed to fetch agents',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}

// POST /api/agents - Create new agent
export async function POST(request: NextRequest) {
  try {
    const body: CreateUserRequest = await request.json()
    
    // Validate required fields
    if (!body.name || !body.email) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Name and email are required',
          },
        },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: body.email },
    })

    if (existingUser) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: 'User with this email already exists',
          },
        },
        { status: 409 }
      )
    }

    // Get organization ID (for now using demo org, later from auth)
    const organizationId = await getDemoOrgId()

    // Create agent
    const agent = await prisma.user.create({
      data: {
        name: body.name,
        email: body.email,
        organizationId,
        maxConcurrentTasks: body.maxConcurrentTasks || 5,
        status: body.status || UserStatus.AVAILABLE,
        currentTaskCount: 0,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            assignedTasks: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: agent,
      message: 'Agent created successfully',
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating agent:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'CREATE_AGENT_FAILED',
          message: 'Failed to create agent',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}
