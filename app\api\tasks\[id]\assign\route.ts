import { NextRequest, NextResponse } from 'next/server'
import { autoAssignTask, createTaskRouter } from '@/lib/task-router'
import { getDemoOrgId } from '@/lib/db-utils'
import { prisma } from '@/lib/prisma'

// POST /api/tasks/[id]/assign - Manually trigger task assignment
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const strategy = body.strategy || 'round_robin'

    // Validate strategy
    const validStrategies = ['round_robin', 'least_loaded', 'weighted_round_robin']
    if (!validStrategies.includes(strategy)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_STRATEGY',
            message: `Invalid routing strategy. Must be one of: ${validStrategies.join(', ')}`,
          },
        },
        { status: 400 }
      )
    }

    // Check if task exists
    const task = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        assignedUser: {
          select: { name: true, email: true }
        }
      }
    })

    if (!task) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TASK_NOT_FOUND',
            message: 'Task not found',
          },
        },
        { status: 404 }
      )
    }

    if (task.assignedTo) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TASK_ALREADY_ASSIGNED',
            message: `Task is already assigned to ${task.assignedUser?.name}`,
          },
        },
        { status: 409 }
      )
    }

    // Get organization ID
    const organizationId = await getDemoOrgId()

    // Attempt assignment
    const routingResult = await autoAssignTask(params.id, organizationId, strategy)

    if (!routingResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ASSIGNMENT_FAILED',
            message: routingResult.error || routingResult.reason || 'Assignment failed',
          },
        },
        { status: 400 }
      )
    }

    // Get updated task
    const updatedTask = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        assignedUser: {
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedTask,
      message: `Task assigned to ${routingResult.assignedAgent?.name} using ${strategy} strategy`,
      routing: {
        strategy,
        assignedAgent: routingResult.assignedAgent?.name,
        reason: routingResult.reason,
      },
    })

  } catch (error) {
    console.error('Error in manual task assignment:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'ASSIGNMENT_ERROR',
          message: 'Failed to assign task',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}

// GET /api/tasks/[id]/assign - Get assignment recommendations
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if task exists
    const task = await prisma.task.findUnique({
      where: { id: params.id },
      include: {
        assignedUser: {
          select: { name: true, email: true }
        }
      }
    })

    if (!task) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'TASK_NOT_FOUND',
            message: 'Task not found',
          },
        },
        { status: 404 }
      )
    }

    if (task.assignedTo) {
      return NextResponse.json({
        success: true,
        data: {
          taskId: params.id,
          currentAssignment: {
            agentId: task.assignedTo,
            agentName: task.assignedUser?.name,
            agentEmail: task.assignedUser?.email,
          },
          canReassign: true,
          recommendations: [],
        },
        message: 'Task is already assigned',
      })
    }

    // Get organization ID and create router
    const organizationId = await getDemoOrgId()
    const router = createTaskRouter(organizationId)

    // Get eligible agents (this gives us the recommendation data)
    const eligibleAgents = await (router as any).getEligibleAgents()

    const recommendations = eligibleAgents.map((agent: any) => ({
      agentId: agent.id,
      agentName: agent.name,
      agentEmail: agent.email,
      currentTasks: agent.currentTaskCount,
      maxTasks: agent.maxConcurrentTasks,
      utilizationRate: agent.utilizationRate,
      lastAssignedAt: agent.lastAssignedAt,
      status: agent.status,
    }))

    // Sort recommendations by suitability (lowest utilization first)
    recommendations.sort((a, b) => a.utilizationRate - b.utilizationRate)

    return NextResponse.json({
      success: true,
      data: {
        taskId: params.id,
        currentAssignment: null,
        canAssign: recommendations.length > 0,
        recommendations,
        routingStats: await router.getRoutingStats(),
      },
      message: recommendations.length > 0 
        ? `Found ${recommendations.length} eligible agents` 
        : 'No eligible agents available',
    })

  } catch (error) {
    console.error('Error getting assignment recommendations:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'RECOMMENDATION_ERROR',
          message: 'Failed to get assignment recommendations',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      },
      { status: 500 }
    )
  }
}
