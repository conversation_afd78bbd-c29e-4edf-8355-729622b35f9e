import {
  PrismaClient,
  UserStatus,
  UserRole,
  TaskStatus,
  TaskPriority,
  TaskEventType,
} from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting database seed...");

  // Create demo organization
  const organization = await prisma.organization.upsert({
    where: { id: "demo-org" },
    update: {},
    create: {
      id: "demo-org",
      name: "Demo Organization",
    },
  });

  console.log("✅ Created organization:", organization.name);

  // Create demo users (admin and agents)
  console.log("👥 Creating demo users...");

  // Hash passwords for demo users
  const defaultPassword = await bcrypt.hash("demo123", 12);

  const users = await Promise.all([
    // Admin user
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Admin User",
        email: "<EMAIL>",
        password: defaultPassword,
        role: UserRole.ADMIN,
        organizationId: organization.id,
        maxConcurrentTasks: 10,
        currentTaskCount: 0,
        status: UserStatus.AVAILABLE,
      },
    }),
    // Agent users
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Alice Johnson",
        email: "<EMAIL>",
        password: defaultPassword,
        role: UserRole.AGENT,
        organizationId: organization.id,
        maxConcurrentTasks: 5,
        currentTaskCount: 2,
        status: UserStatus.AVAILABLE,
      },
    }),
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Bob Smith",
        email: "<EMAIL>",
        password: defaultPassword,
        role: UserRole.AGENT,
        organizationId: organization.id,
        maxConcurrentTasks: 3,
        currentTaskCount: 1,
        status: UserStatus.AVAILABLE,
      },
    }),
    prisma.user.upsert({
      where: { email: "<EMAIL>" },
      update: {},
      create: {
        name: "Charlie Brown",
        email: "<EMAIL>",
        password: defaultPassword,
        role: UserRole.AGENT,
        organizationId: organization.id,
        maxConcurrentTasks: 4,
        currentTaskCount: 0,
        status: UserStatus.AWAY,
      },
    }),
  ]);

  console.log("✅ Created users:", users.map((u) => u.name).join(", "));

  // Create demo tasks
  const tasks = await Promise.all([
    prisma.task.create({
      data: {
        title: "Customer Support Ticket #1001",
        description: "Customer having trouble with login functionality",
        priority: TaskPriority.HIGH,
        type: "support",
        estimatedDuration: 30,
        source: "email",
        organizationId: organization.id,
        assignedTo: users[0].id,
        assignedAt: new Date(),
        status: TaskStatus.IN_PROGRESS,
        responseDeadline: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
      },
    }),
    prisma.task.create({
      data: {
        title: "Sales Lead Follow-up",
        description: "Follow up with potential enterprise client",
        priority: TaskPriority.MEDIUM,
        type: "sales",
        estimatedDuration: 45,
        source: "crm",
        organizationId: organization.id,
        assignedTo: users[1].id,
        assignedAt: new Date(),
        status: TaskStatus.ASSIGNED,
        responseDeadline: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      },
    }),
    prisma.task.create({
      data: {
        title: "Code Review Request",
        description: "Review pull request for new feature implementation",
        priority: TaskPriority.MEDIUM,
        type: "review",
        estimatedDuration: 60,
        source: "github",
        organizationId: organization.id,
        status: TaskStatus.PENDING,
        responseDeadline: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
      },
    }),
    prisma.task.create({
      data: {
        title: "Urgent Bug Fix",
        description: "Critical bug affecting payment processing",
        priority: TaskPriority.URGENT,
        type: "bug",
        estimatedDuration: 120,
        source: "monitoring",
        organizationId: organization.id,
        assignedTo: users[0].id,
        assignedAt: new Date(),
        status: TaskStatus.IN_PROGRESS,
        responseDeadline: new Date(Date.now() + 1 * 60 * 60 * 1000), // 1 hour from now
      },
    }),
  ]);

  console.log("✅ Created tasks:", tasks.map((t) => t.title).join(", "));

  // Create task events for audit trail
  for (const task of tasks) {
    await prisma.taskEvent.create({
      data: {
        taskId: task.id,
        type: TaskEventType.CREATED,
        data: {
          title: task.title,
          priority: task.priority,
          type: task.type,
        },
      },
    });

    if (task.assignedTo) {
      await prisma.taskEvent.create({
        data: {
          taskId: task.id,
          userId: task.assignedTo,
          type: TaskEventType.ASSIGNED,
          data: {
            assignedTo: task.assignedTo,
            assignedAt: task.assignedAt,
          },
        },
      });
    }
  }

  console.log("✅ Created task events");

  console.log("🎉 Database seed completed successfully!");
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error("❌ Seed failed:", e);
    await prisma.$disconnect();
    process.exit(1);
  });
